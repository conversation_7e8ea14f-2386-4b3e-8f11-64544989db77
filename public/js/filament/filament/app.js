(()=>{var ee=Object.create,q=Object.defineProperty,te=Object.getPrototypeOf,re=Object.prototype.hasOwnProperty,ne=Object.getOwnPropertyNames,ae=Object.getOwnPropertyDescriptor,ie=r=>q(r,"__esModule",{value:!0}),oe=(r,a)=>()=>(a||(a={exports:{}},r(a.exports,a)),a.exports),se=(r,a,i)=>{if(a&&typeof a=="object"||typeof a=="function")for(let l of ne(a))!re.call(r,l)&&l!=="default"&&q(r,l,{get:()=>a[l],enumerable:!(i=ae(a,l))||i.enumerable});return r},fe=r=>se(ie(q(r!=null?ee(te(r)):{},"default",r&&r.__esModule&&"default"in r?{get:()=>r.default,enumerable:!0}:{value:r,enumerable:!0})),r),le=oe((r,a)=>{(function(i,l,b){if(!i)return;for(var d={8:"backspace",9:"tab",13:"enter",16:"shift",17:"ctrl",18:"alt",20:"capslock",27:"esc",32:"space",33:"pageup",34:"pagedown",35:"end",36:"home",37:"left",38:"up",39:"right",40:"down",45:"ins",46:"del",91:"meta",93:"meta",224:"meta"},g={106:"*",107:"+",109:"-",110:".",111:"/",186:";",187:"=",188:",",189:"-",190:".",191:"/",192:"`",219:"[",220:"\\",221:"]",222:"'"},w={"~":"`","!":"1","@":"2","#":"3",$:"4","%":"5","^":"6","&":"7","*":"8","(":"9",")":"0",_:"-","+":"=",":":";",'"':"'","<":",",">":".","?":"/","|":"\\"},x={option:"alt",command:"meta",return:"enter",escape:"esc",plus:"+",mod:/Mac|iPod|iPhone|iPad/.test(navigator.platform)?"meta":"ctrl"},S,y=1;y<20;++y)d[111+y]="f"+y;for(y=0;y<=9;++y)d[y+96]=y.toString();function C(e,t,o){if(e.addEventListener){e.addEventListener(t,o,!1);return}e.attachEvent("on"+t,o)}function G(e){if(e.type=="keypress"){var t=String.fromCharCode(e.which);return e.shiftKey||(t=t.toLowerCase()),t}return d[e.which]?d[e.which]:g[e.which]?g[e.which]:String.fromCharCode(e.which).toLowerCase()}function R(e,t){return e.sort().join(",")===t.sort().join(",")}function F(e){var t=[];return e.shiftKey&&t.push("shift"),e.altKey&&t.push("alt"),e.ctrlKey&&t.push("ctrl"),e.metaKey&&t.push("meta"),t}function J(e){if(e.preventDefault){e.preventDefault();return}e.returnValue=!1}function B(e){if(e.stopPropagation){e.stopPropagation();return}e.cancelBubble=!0}function E(e){return e=="shift"||e=="ctrl"||e=="alt"||e=="meta"}function W(){if(!S){S={};for(var e in d)e>95&&e<112||d.hasOwnProperty(e)&&(S[d[e]]=e)}return S}function X(e,t,o){return o||(o=W()[e]?"keydown":"keypress"),o=="keypress"&&t.length&&(o="keydown"),o}function Y(e){return e==="+"?["+"]:(e=e.replace(/\+{2}/g,"+plus"),e.split("+"))}function T(e,t){var o,v,k,M=[];for(o=Y(e),k=0;k<o.length;++k)v=o[k],x[v]&&(v=x[v]),t&&t!="keypress"&&w[v]&&(v=w[v],M.push("shift")),E(v)&&M.push(v);return t=X(v,M,t),{key:v,modifiers:M,action:t}}function I(e,t){return e===null||e===l?!1:e===t?!0:I(e.parentNode,t)}function h(e){var t=this;if(e=e||l,!(t instanceof h))return new h(e);t.target=e,t._callbacks={},t._directMap={};var o={},v,k=!1,M=!1,O=!1;function L(n){n=n||{};var f=!1,p;for(p in o){if(n[p]){f=!0;continue}o[p]=0}f||(O=!1)}function U(n,f,p,s,c,m){var u,_,P=[],A=p.type;if(!t._callbacks[n])return[];for(A=="keyup"&&E(n)&&(f=[n]),u=0;u<t._callbacks[n].length;++u)if(_=t._callbacks[n][u],!(!s&&_.seq&&o[_.seq]!=_.level)&&A==_.action&&(A=="keypress"&&!p.metaKey&&!p.ctrlKey||R(f,_.modifiers))){var Z=!s&&_.combo==c,$=s&&_.seq==s&&_.level==m;(Z||$)&&t._callbacks[n].splice(u,1),P.push(_)}return P}function K(n,f,p,s){t.stopCallback(f,f.target||f.srcElement,p,s)||n(f,p)===!1&&(J(f),B(f))}t._handleKey=function(n,f,p){var s=U(n,f,p),c,m={},u=0,_=!1;for(c=0;c<s.length;++c)s[c].seq&&(u=Math.max(u,s[c].level));for(c=0;c<s.length;++c){if(s[c].seq){if(s[c].level!=u)continue;_=!0,m[s[c].seq]=1,K(s[c].callback,p,s[c].combo,s[c].seq);continue}_||K(s[c].callback,p,s[c].combo)}var P=p.type=="keypress"&&M;p.type==O&&!E(n)&&!P&&L(m),M=_&&p.type=="keydown"};function D(n){typeof n.which!="number"&&(n.which=n.keyCode);var f=G(n);if(f){if(n.type=="keyup"&&k===f){k=!1;return}t.handleKey(f,F(n),n)}}function z(){clearTimeout(v),v=setTimeout(L,1e3)}function Q(n,f,p,s){o[n]=0;function c(A){return function(){O=A,++o[n],z()}}function m(A){K(p,A,n),s!=="keyup"&&(k=G(A)),setTimeout(L,10)}for(var u=0;u<f.length;++u){var _=u+1===f.length,P=_?m:c(s||T(f[u+1]).action);j(f[u],P,s,n,u)}}function j(n,f,p,s,c){t._directMap[n+":"+p]=f,n=n.replace(/\s+/g," ");var m=n.split(" "),u;if(m.length>1){Q(n,m,f,p);return}u=T(n,p),t._callbacks[u.key]=t._callbacks[u.key]||[],U(u.key,u.modifiers,{type:u.action},s,n,c),t._callbacks[u.key][s?"unshift":"push"]({callback:f,modifiers:u.modifiers,action:u.action,seq:s,level:c,combo:n})}t._bindMultiple=function(n,f,p){for(var s=0;s<n.length;++s)j(n[s],f,p)},C(e,"keypress",D),C(e,"keydown",D),C(e,"keyup",D)}h.prototype.bind=function(e,t,o){var v=this;return e=e instanceof Array?e:[e],v._bindMultiple.call(v,e,t,o),v},h.prototype.unbind=function(e,t){var o=this;return o.bind.call(o,e,function(){},t)},h.prototype.trigger=function(e,t){var o=this;return o._directMap[e+":"+t]&&o._directMap[e+":"+t]({},e),o},h.prototype.reset=function(){var e=this;return e._callbacks={},e._directMap={},e},h.prototype.stopCallback=function(e,t){var o=this;if((" "+t.className+" ").indexOf(" mousetrap ")>-1||I(t,o.target))return!1;if("composedPath"in e&&typeof e.composedPath=="function"){var v=e.composedPath()[0];v!==e.target&&(t=v)}return t.tagName=="INPUT"||t.tagName=="SELECT"||t.tagName=="TEXTAREA"||t.isContentEditable},h.prototype.handleKey=function(){var e=this;return e._handleKey.apply(e,arguments)},h.addKeycodes=function(e){for(var t in e)e.hasOwnProperty(t)&&(d[t]=e[t]);S=null},h.init=function(){var e=h(l);for(var t in e)t.charAt(0)!=="_"&&(h[t]=function(o){return function(){return e[o].apply(e,arguments)}}(t))},h.init(),i.Mousetrap=h,typeof a<"u"&&a.exports&&(a.exports=h),typeof define=="function"&&define.amd&&define(function(){return h})})(typeof window<"u"?window:null,typeof window<"u"?document:null)}),N=fe(le());(function(r){if(r){var a={},i=r.prototype.stopCallback;r.prototype.stopCallback=function(l,b,d,g){var w=this;return w.paused?!0:a[d]||a[g]?!1:i.call(w,l,b,d)},r.prototype.bindGlobal=function(l,b,d){var g=this;if(g.bind(l,b,d),l instanceof Array){for(var w=0;w<l.length;w++)a[l[w]]=!0;return}a[l]=!0},r.init()}})(typeof Mousetrap<"u"?Mousetrap:void 0);var ue=r=>{r.directive("mousetrap",(a,{modifiers:i,expression:l},{evaluate:b})=>{let d=()=>l?b(l):a.click();i=i.map(g=>g.replace(/--/g," ").replace(/-/g,"+").replace(/\bslash\b/g,"/")),i.includes("global")&&(i=i.filter(g=>g!=="global"),N.default.bindGlobal(i,g=>{g.preventDefault(),d()})),N.default.bind(i,g=>{g.preventDefault(),d()})})},V=ue;var H=()=>({isOpen:window.Alpine.$persist(!0).as("isOpen"),collapsedGroups:window.Alpine.$persist(null).as("collapsedGroups"),groupIsCollapsed(r){return this.collapsedGroups.includes(r)},collapseGroup(r){this.collapsedGroups.includes(r)||(this.collapsedGroups=this.collapsedGroups.concat(r))},toggleCollapsedGroup(r){this.collapsedGroups=this.collapsedGroups.includes(r)?this.collapsedGroups.filter(a=>a!==r):this.collapsedGroups.concat(r)},close(){this.isOpen=!1},open(){this.isOpen=!0}});document.addEventListener("alpine:init",()=>{let r=localStorage.getItem("theme")??getComputedStyle(document.documentElement).getPropertyValue("--default-theme-mode");window.Alpine.store("theme",r==="dark"||r==="system"&&window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light"),window.addEventListener("theme-changed",a=>{let i=a.detail;localStorage.setItem("theme",i),i==="system"&&(i=window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light"),window.Alpine.store("theme",i)}),window.matchMedia("(prefers-color-scheme: dark)").addEventListener("change",a=>{localStorage.getItem("theme")==="system"&&window.Alpine.store("theme",a.matches?"dark":"light")}),window.Alpine.effect(()=>{window.Alpine.store("theme")==="dark"?document.documentElement.classList.add("dark"):document.documentElement.classList.remove("dark")})});document.addEventListener("DOMContentLoaded",()=>{setTimeout(()=>{let r=document.querySelector(".fi-main-sidebar .fi-sidebar-item.fi-active");if((!r||r.offsetParent===null)&&(r=document.querySelector(".fi-main-sidebar .fi-sidebar-group.fi-active")),!r||r.offsetParent===null)return;let a=document.querySelector(".fi-main-sidebar .fi-sidebar-nav");a&&a.scrollTo(0,r.offsetTop-window.innerHeight/2)},10)});window.setUpUnsavedDataChangesAlert=({body:r,livewireComponent:a,$wire:i})=>{window.addEventListener("beforeunload",l=>{window.jsMd5(JSON.stringify(i.data).replace(/\\/g,""))===i.savedDataHash||i?.__instance?.effects?.redirect||(l.preventDefault(),l.returnValue=!0)})};window.setUpSpaModeUnsavedDataChangesAlert=({body:r,resolveLivewireComponentUsing:a,$wire:i})=>{let l=()=>i?.__instance?.effects?.redirect?!1:window.jsMd5(JSON.stringify(i.data).replace(/\\/g,""))!==i.savedDataHash,b=()=>confirm(r);document.addEventListener("livewire:navigate",d=>{if(typeof a()<"u"){if(!l()||b())return;d.preventDefault()}}),window.addEventListener("beforeunload",d=>{l()&&(d.preventDefault(),d.returnValue=!0)})};window.setUpUnsavedActionChangesAlert=({resolveLivewireComponentUsing:r,$wire:a})=>{window.addEventListener("beforeunload",i=>{if(!(typeof r()>"u")&&(a.mountedActions?.length??0)&&!a?.__instance?.effects?.redirect){i.preventDefault(),i.returnValue=!0;return}})};document.addEventListener("alpine:init",()=>{window.Alpine.plugin(V),window.Alpine.store("sidebar",H())});})();
